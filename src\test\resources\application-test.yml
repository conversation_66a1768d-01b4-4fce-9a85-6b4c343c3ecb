spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password:
  h2:
    console:
      enabled: false
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    database-platform: org.hibernate.dialect.H2Dialect
  sql:
    init:
      mode: never

jwt:
  secret: testSecretKeyForAdviceApiApplicationThatIsLongEnoughForHS256Algorithm
  expiration: 3600000 # 1 hour for tests

logging:
  level:
    com.descenedigital: WARN
    org.springframework.security: WARN
    org.hibernate: WARN
