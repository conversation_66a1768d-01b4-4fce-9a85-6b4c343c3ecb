spring:
  application:
    name: AdviceApi
  datasource:
    url: jdbc:h2:mem:advice-db
    driver-class-name: org.h2.Driver
    username: sa
    password:
  h2:
    console:
      enabled: true
      path: /h2-console
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    database-platform: org.hibernate.dialect.H2Dialect
    properties:
      hibernate:
        format_sql: true
  sql:
    init:
      mode: always
      data-locations: classpath:data.sql

server:
  port: 8080
  error:
    include-message: always
    include-binding-errors: always

# JWT Configuration
jwt:
  secret: mySecretKeyForAdviceApiApplicationThatIsLongEnoughForHS256Algorithm
  expiration: 86400000 # 24 hours in milliseconds

# Logging Configuration
logging:
  level:
    com.descenedigital: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# Swagger Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
    tagsSorter: alpha