package com.descenedigital.service;

import com.descenedigital.config.JwtUtil;
import com.descenedigital.dto.auth.AuthResponse;
import com.descenedigital.dto.auth.LoginRequest;
import com.descenedigital.dto.auth.RegisterRequest;
import com.descenedigital.dto.user.UserResponse;
import com.descenedigital.model.Role;
import com.descenedigital.model.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;

import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuthServiceTest {

    @Mock
    private AuthenticationManager authenticationManager;

    @Mock
    private UserService userService;

    @Mock
    private JwtUtil jwtUtil;

    @Mock
    private Authentication authentication;

    @InjectMocks
    private AuthService authService;

    private User testUser;
    private LoginRequest loginRequest;
    private RegisterRequest registerRequest;
    private UserResponse userResponse;

    @BeforeEach
    void setUp() {
        testUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .roles(Set.of(Role.USER))
                .build();

        loginRequest = new LoginRequest("testuser", "password");
        
        registerRequest = new RegisterRequest();
        registerRequest.setUsername("testuser");
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setPassword("password");
        registerRequest.setFirstName("Test");
        registerRequest.setLastName("User");

        userResponse = UserResponse.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .roles(Set.of("USER"))
                .build();
    }

    @Test
    void login_ShouldReturnAuthResponse_WhenCredentialsAreValid() {
        // Given
        String expectedToken = "jwt-token";
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
                .thenReturn(authentication);
        when(authentication.getPrincipal()).thenReturn(testUser);
        when(jwtUtil.generateToken(eq(testUser), any(Map.class))).thenReturn(expectedToken);

        // When
        AuthResponse result = authService.login(loginRequest);

        // Then
        assertNotNull(result);
        assertEquals(expectedToken, result.getToken());
        assertEquals("Bearer", result.getType());
        assertEquals("testuser", result.getUsername());
        assertEquals("<EMAIL>", result.getEmail());
        assertTrue(result.getRoles().contains("USER"));
        assertEquals("Login successful", result.getMessage());

        verify(authenticationManager).authenticate(any(UsernamePasswordAuthenticationToken.class));
        verify(userService).updateLastLogin("testuser");
        verify(jwtUtil).generateToken(eq(testUser), any(Map.class));
    }

    @Test
    void register_ShouldReturnAuthResponse_WhenRegistrationIsSuccessful() {
        // Given
        String expectedToken = "jwt-token";
        when(userService.createUser(registerRequest)).thenReturn(userResponse);
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
                .thenReturn(authentication);
        when(authentication.getPrincipal()).thenReturn(testUser);
        when(jwtUtil.generateToken(eq(testUser), any(Map.class))).thenReturn(expectedToken);

        // When
        AuthResponse result = authService.register(registerRequest);

        // Then
        assertNotNull(result);
        assertEquals(expectedToken, result.getToken());
        assertEquals("Bearer", result.getType());
        assertEquals("testuser", result.getUsername());
        assertEquals("<EMAIL>", result.getEmail());
        assertTrue(result.getRoles().contains("USER"));
        assertEquals("Registration and login successful", result.getMessage());

        verify(userService).createUser(registerRequest);
        verify(authenticationManager).authenticate(any(UsernamePasswordAuthenticationToken.class));
        verify(userService).updateLastLogin("testuser");
        verify(jwtUtil).generateToken(eq(testUser), any(Map.class));
    }
}
