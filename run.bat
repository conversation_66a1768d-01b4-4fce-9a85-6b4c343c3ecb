@echo off
echo Starting Advice API Application...
echo.
echo Compiling the application...
javac -cp "src/main/java" -d "target/classes" src/main/java/com/descenedigital/*.java src/main/java/com/descenedigital/*/*.java src/main/java/com/descenedigital/*/*/*.java

echo.
echo Application compiled successfully!
echo.
echo To run the application, you need to:
echo 1. Install Maven
echo 2. Run: mvn spring-boot:run
echo.
echo Or use your IDE to run the AdviceApiApplication.java main class
echo.
echo Once running, access:
echo - API: http://localhost:8080
echo - Swagger UI: http://localhost:8080/swagger-ui/index.html
echo - H2 Console: http://localhost:8080/h2-console
echo.
echo Sample login credentials:
echo - Username: admin, Password: password (ADMIN role)
echo - Username: john_doe, Password: password (USER role)
echo.
pause
